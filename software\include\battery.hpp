#ifndef BATTERY_HPP
#define BATTERY_HPP
#include <stdbool.h>
#include <stdint.h>
void read_battery_status_1(const uint8_t message[8], float *battery_voltage,
                           float *battery_current, float *soc);
void read_battery_status_2(const uint8_t message[8]);
void read_cell_voltage(const uint8_t message[8]);
void read_cell_temperature(const uint8_t message[8]);
void read_bms_information(const uint8_t message[8]);
void read_switch_status(const uint8_t message);
void read_control_information(const uint8_t message[8]);
void read_charging_request(const uint8_t message[8]);
#endif // BATTERY_HPP
