#ifndef OBJECT_DICTIONARY_HPP
#define OBJECT_DICTIONARY_HPP

#include <stdint.h>

const uint16_t SDO_SEND_MESSAGE_CAN_IDENTIFIER = 0x600;
const uint16_t SDO_RECIEVE_MESSAGE_CAN_IDENTIFIER = 0x580;
const uint16_t TPDO1_RECIEVE_MESSAGE_CAN_IDENTIFIER = 0x180;

#define SDO_READ_SEND_PARAMETER_CMD_WORD 0x40
#define SDO_READ_SEND_PARAMETER_CMD_WORD 0x40
#define SDO_READ_RECIEVE_PARAMETER_CMD_WORD_1_BYTE 0x4F
#define SDO_READ_RECIEVE_PARAMETER_CMD_WORD_2_BYTE 0x4B
#define SDO_READ_RECIEVE_PARAMETER_CMD_WORD_4_BYTE 0x43
#define SDO_READ_RECIEVE_PARAMETER_CMD_WORD_ERROR 0x80

#define DOWNLOAD_COMMAND_WORD_32_BITS 0x23
#define DOWNLOAD_COMMAND_WORD_16_BITS 0x2B
#define DOWNLOAD_COMMAND_WORD_8_BITS 0x2F

#define PRODUCER_HEARTBEAT_TIME_IDX 0x1017
#define PRODUCER_HEARTBEAT_TIME_SUB_IDX 0x00

#define INVERT_DIR_IDX 0x607E
#define INVERT_DIR_SUB_IDX 0x00
#define INVERT_DIR_CCW 0x00
#define INVERT_DIR_CW 0x01

#define OPERATION_MODE_IDX 0x6060
#define OPERATION_MODE_SUB_IDX 0x00
#define OPERATION_MODE_PULSE -4
#define OPERATION_MODE_SPEED_NO_RAMP -3
#define OPERATION_MODE_TORQUE 4
#define OPERATION_MODE_POSITION 1
#define OPERATION_MODE_SPEED_RAMP 3
#define OPERATION_MODE_HOMING 6
#define OPERATION_MODE_INTERPOLATION 6
#define OPERATION_MODE_CSP 8
#define OPERATION_MODE_CSV 9
#define OPERATION_MODE_CST 10

#define CONTROLWORD_IDX 0x6040
#define CONTROLWORD_SUB_IDX 0x00
#define CONTROLWORD_DRIVER_OFF 0x06
#define CONTROLWORD_SPEED_MODE_ENABLE 0x0F
// You always need to disable driver to set a new target in position mode
#define CONTROLWORD_POSITION_MODE_ABSOLUTE_OFF 0x2F
#define CONTROLWORD_POSITION_MODE_ABSOLUTE_ENABLE 0x3F
#define CONTROLWORD_POSITION_MODE_RELATIVE_OFF 0x4F
#define CONTROLWORD_POSITION_MODE_RELATIVE_ENABLE 0x5F

#define TARGET_SPEED_IDX 0x60FF
#define TARGET_SPEED_SUB_IDX 0x00

#define PROFILE_SPEED_IDX 0x6081
#define PROFILE_SPEED_SUB_IDX 0x00

#define PROFILE_ACC_IDX 0x6083
#define PROFILE_ACC_SUB_IDX 0x00

#define PROFILE_DEC_IDX 0x6084
#define PROFILE_DEC_SUB_IDX 0x00

#define TARGET_POSITION_IDX 0x607A
#define TARGET_POSITION_SUB_IDX 0x00

#define ERROR_STATE_IDX 0x2601
#define ERROR_STATE_SUB_IDX 0x00

#define POS_ACTUAL_IDX 0x6063
#define POS_ACTUAL_SUB_IDX 0x00

#define TPDO1_ID_IDX 0x1800
#define TPDO1_ID_SUB_IDX 0x01
#endif // OBJECT_DICTIONARY_HPP
