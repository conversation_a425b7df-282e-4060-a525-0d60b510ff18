#include <Arduino.h>

// C++ Standard Libraries
#include <cstdarg>
#include <cstdint>

// External Libraries for peripherals
#include "Print.h"
#include "core_pins.h"
#include "pins_arduino.h"
#include <ACAN_T4.h>
#include <NativeEthernet.h>
#include <SPI.h>
#include <micro_ros_platformio.h>

// uROS Libraries
#include "rcl/publisher.h"
#include "rcl/subscription.h"
#include <rcl/error_handling.h>
#include <rcl/rcl.h>
#include <rclc/executor.h>
#include <rclc/rclc.h>
#include <rmw_microros/rmw_microros.h>

#include <std_msgs/msg/float32.h>
#include <std_msgs/msg/float32_multi_array.h>
#include <std_msgs/msg/int32.h>
#include <std_msgs/msg/int32_multi_array.h>
#include <std_msgs/msg/u_int16.h>
#include <std_msgs/msg/u_int16_multi_array.h>
#include <std_msgs/msg/u_int8.h>

// Custom Headers
#include "battery.hpp"
#include "battery_object_dictionary.hpp"
#include "config.hpp"
#include "error.hpp"
#include "hex_to_decimal_convert.hpp"
#include "object_dictionary.hpp"
#include "wheel_control.hpp"

#ifndef __IMXRT1062__
#endif

#ifdef TESTING_MODE
#define MODE "Testing Mode"
#elif DEBUG
#define MODE "Debug Mode"
#else
#define MODE "Release Mode"
#endif

// ROS Configurations
#define NODE_NAME "amr_control_board_node"
#define RAW_ENCODER_PUBLISHER_TOPIC "amr/raw_encoder"
#define MOTOR_ERROR_PUBLISHER_TOPIC "amr/motor_error"
#define REQUIRED_TRAVEL_MOTOR_1_SPEED_SUBSCRIBER_TOPIC                         \
  "amr/required_travel_motor_speed"
#define REQUIRED_STEER_MOTOR_1_ANGLE_SUBSCRIBER_TOPIC                          \
  "amr/required_steer_motor_angle"
#define WHEEL_POSE_CONTROL_TOPIC "amr/wheel_pose_control"
#define AMR_POWER_TOPIC "amr/power"
#define AMR_BATTERY_TOPIC "amr/battery"

#define RCCHECK(fn)                                                            \
  {                                                                            \
    rcl_ret_t temp_rc = fn;                                                    \
    if ((temp_rc != RCL_RET_OK)) {                                             \
      error_loop();                                                            \
    }                                                                          \
  }
#define RCSOFTCHECK(fn)                                                        \
  {                                                                            \
    rcl_ret_t temp_rc = fn;                                                    \
    if ((temp_rc != RCL_RET_OK)) {                                             \
    }                                                                          \
  }

// Wheel Parameters
#define FRONT_WHEEL_TRAVEL_MOTOR_NODE_ID 0x01
#define FRONT_WHEEL_STEER_MOTOR_NODE_ID 0x02
#define REAR_LEFT_WHEEL_TRAVEL_MOTOR_NODE_ID 0x03
#define REAR_LEFT_WHEEL_STEER_MOTOR_NODE_ID 0x04
#define REAR_RIGHT_WHEEL_TRAVEL_MOTOR_NODE_ID 0x05
#define REAR_RIGHT_WHEEL_STEER_MOTOR_NODE_ID 0x06

// Frequencies for communications (all times are in mS)
#define ERROR_TIME 100     // 2Hz
#define ENCODER_TIME 2     // 500Hz
#define BATTERY_TIME 10000 // 0.1Hz

#define TOTAL_PUB_SUB_HANDLES 50
// ROS Publishers
rcl_publisher_t raw_encoder_value_publisher; // Int32 multiarray
rcl_publisher_t motor_error_publisher;       // Uint16 multiarray
rcl_publisher_t amr_battery_publisher;       // Uint8 multiarray

// ROS Subscribers
rcl_subscription_t wheel_pose_control_subscriber; // Float32 MultiArray
rcl_subscription_t amr_motor_power_subscriber;

// ROS Datatypes
bool wheel_pose_control_msg_recieved = false;
std_msgs__msg__Float32MultiArray wheel_pose_control_msg;
std_msgs__msg__UInt8 amr_power_msg;
std_msgs__msg__Float32MultiArray amr_battery_msg;
std_msgs__msg__Int32MultiArray raw_encoder_msg;
std_msgs__msg__UInt16MultiArray motor_error_msg;

rclc_executor_t executor;
rclc_support_t support;
rcl_allocator_t allocator;
rcl_node_t node;
rcl_timer_t encoder_timer;
rcl_timer_t battery_timer;

wheelControl frontWheel(1, FRONT_WHEEL_TRAVEL_MOTOR_NODE_ID,
                        FRONT_WHEEL_STEER_MOTOR_NODE_ID, 103834,
                        MOTOR_HEARTBEAT_TIME_MS);
wheelControl rearLeftWheel(2, REAR_LEFT_WHEEL_TRAVEL_MOTOR_NODE_ID,
                           REAR_LEFT_WHEEL_STEER_MOTOR_NODE_ID, 65939,
                           MOTOR_HEARTBEAT_TIME_MS);
wheelControl rearRightWheel(3, REAR_RIGHT_WHEEL_TRAVEL_MOTOR_NODE_ID,
                            REAR_RIGHT_WHEEL_STEER_MOTOR_NODE_ID, 74048,
                            MOTOR_HEARTBEAT_TIME_MS);
wheelControl *wheels[] = {&frontWheel, &rearLeftWheel, &rearRightWheel};

// Function Definition
void encoder_timer_callback(rcl_timer_t *timer, int64_t last_call_time);
void battery_timer_callback(rcl_timer_t *timer, int64_t last_call_time);
void wheel_pose_control_callback(const void *msgin);
void amr_power_callback(const void *msgin);

// Error handle loop
void error_loop() {
  Serial.println("uROS Error Has occured!!");
  while (1) {
    delay(50);
    digitalWrite(LED_BUILTIN, !digitalRead(LED_BUILTIN));
  }
}

// ------------------------------------------------------ //
// Publishers
// ------------------------------------------------------ //
void setup_all_publishers() {

  // Set Encoder publisher
  RCCHECK(rclc_publisher_init_default(
      &raw_encoder_value_publisher, &node,
      ROSIDL_GET_MSG_TYPE_SUPPORT(std_msgs, msg, Int32MultiArray),
      RAW_ENCODER_PUBLISHER_TOPIC));
  raw_encoder_msg.data.capacity = 6;
  raw_encoder_msg.data.size = 6;
  raw_encoder_msg.data.data =
      (int32_t *)malloc(sizeof(int32_t) * raw_encoder_msg.data.capacity);
  if (raw_encoder_msg.data.data == NULL) {
    Serial.println("Raw Encoder Message Memory allocation failed!");
  }

  RCCHECK(rclc_publisher_init_default(
      &motor_error_publisher, &node,
      ROSIDL_GET_MSG_TYPE_SUPPORT(std_msgs, msg, UInt16MultiArray),
      MOTOR_ERROR_PUBLISHER_TOPIC));
  motor_error_msg.data.capacity = 6;
  motor_error_msg.data.size = 6;
  motor_error_msg.data.data =
      (uint16_t *)malloc(sizeof(uint16_t) * motor_error_msg.data.capacity);
  if (motor_error_msg.data.data == NULL) {
    Serial.println("Motor Error Message Memory allocation failed!");
  }

  RCCHECK(rclc_publisher_init_default(
      &amr_battery_publisher, &node,
      ROSIDL_GET_MSG_TYPE_SUPPORT(std_msgs, msg, Float32MultiArray),
      AMR_BATTERY_TOPIC));
  amr_battery_msg.data.capacity = 3;
  amr_battery_msg.data.size = 3;
  amr_battery_msg.data.data =
      (float *)malloc(sizeof(float) * amr_battery_msg.data.capacity);
  if (amr_battery_msg.data.data == NULL) {
    Serial.println("Battery Information Message Memory allocation failed!");
  }

  //  RCCHECK(rclc_publisher_init_default(
  //      &calculated_encoder_publisher, &node,
  //      ROSIDL_GET_MSG_TYPE_SUPPORT(std_msgs, msg, Float32MultiArray),
  //      CALCULATED_ENCODER_PUBLISHER_TOPIC));
  //
  Serial.println("Publishers Initialization Complete");
}

void setup_all_timers() {
  RCCHECK(rclc_timer_init_default(&encoder_timer, &support,
                                  RCL_MS_TO_NS(ENCODER_TIME),
                                  encoder_timer_callback));
  RCCHECK(rclc_timer_init_default(&battery_timer, &support,
                                  RCL_MS_TO_NS(BATTERY_TIME),
                                  battery_timer_callback));
  Serial.println("Timers Initialization Complete");
}

void encoder_timer_callback(rcl_timer_t *timer, int64_t last_call_time) {
  RCLC_UNUSED(last_call_time);
  if (timer != NULL) {
    RCSOFTCHECK(
        rcl_publish(&raw_encoder_value_publisher, &raw_encoder_msg, NULL));

    // TODO: Fetch calculated encoder values here
    //    RCSOFTCHECK(rcl_publish(&calculated_encoder_publisher, &msgout,
    //    NULL));
  }
}

void battery_timer_callback(rcl_timer_t *timer, int64_t last_call_time) {
  RCLC_UNUSED(last_call_time);
  if (timer != NULL) {
    RCSOFTCHECK(rcl_publish(&amr_battery_publisher, &amr_battery_msg, NULL));
  }
}

void publish_error_msg(void) {
  RCSOFTCHECK(rcl_publish(&motor_error_publisher, &motor_error_msg, NULL));
}

// ------------------------------------------------------ //
// Subscribers
// ------------------------------------------------------ //
void setup_all_subscribers() {
  RCCHECK(rclc_subscription_init_best_effort(
      &wheel_pose_control_subscriber, &node,
      ROSIDL_GET_MSG_TYPE_SUPPORT(std_msgs, msg, Float32MultiArray),
      WHEEL_POSE_CONTROL_TOPIC));
  wheel_pose_control_msg.data.capacity = 6;
  wheel_pose_control_msg.data.size = 6;
  wheel_pose_control_msg.data.data =
      (float *)malloc(sizeof(float) * wheel_pose_control_msg.data.capacity);
  if (wheel_pose_control_msg.data.data == NULL) {
    Serial.println("Memory allocation failed!");
  }

  RCCHECK(rclc_subscription_init_best_effort(
      &amr_motor_power_subscriber, &node,
      ROSIDL_GET_MSG_TYPE_SUPPORT(std_msgs, msg, UInt8), AMR_POWER_TOPIC));
  Serial.println("Subscribers Initialization Complete");
}
void setup_executor() {
  // create executor
  RCCHECK(rclc_executor_init(&executor, &support.context, TOTAL_PUB_SUB_HANDLES,
                             &allocator));
  RCCHECK(rclc_executor_add_timer(&executor, &encoder_timer));
  RCCHECK(rclc_executor_add_timer(&executor, &battery_timer));
  RCCHECK(rclc_executor_add_subscription(
      &executor, &wheel_pose_control_subscriber, &wheel_pose_control_msg,
      &wheel_pose_control_callback, ON_NEW_DATA));
  RCCHECK(rclc_executor_add_subscription(&executor, &amr_motor_power_subscriber,
                                         &amr_power_msg, &amr_power_callback,
                                         ON_NEW_DATA));
}

void wheel_pose_control_callback(const void *msgin) {
  RCLC_UNUSED(msgin);
  wheel_pose_control_msg_recieved = true;
}

void amr_power_callback(const void *msgin) {
  RCLC_UNUSED(msgin);
  Serial.println("Emergency Stop Requested");
  for (auto wheel : wheels) {
    wheel->m_emergencyStopWheel();
  }
}

byte local_mac[] = {0xAA, 0xBB, 0xCC, 0xEE, 0xDD, 0xFF};
IPAddress local_ip(192, 168, 1, 200);
IPAddress agent_ip(192, 168, 1, 1);
size_t agent_port = 8888;

void setup() {
  // Configure serial transport
  Serial.begin(926100);
  initialize_diagnostic_error_handler();
  pinMode(LED_BUILTIN, OUTPUT);
  while (!Serial) {
    delay(50);
    digitalWrite(LED_BUILTIN, !digitalRead(LED_BUILTIN));
  }
  Serial.println("Serial Connection Active!");
  Serial.print("Operating AMR in: ");
  Serial.println(MODE);

  ACAN_T4_Settings can1Settings(1000 * 1000);
  ACAN_T4_Settings can3Settings(250 * 1000);
  uint32_t errorCode1 = ACAN_T4::can1.begin(can1Settings);
  uint32_t errorCode2 = ACAN_T4::can3.begin(can3Settings);
  if (0 == errorCode1) {
    Serial.println("can1 ok");
  } else {
    Serial.print("Error can1: 0x");
    Serial.println(errorCode1, HEX);
  }
  if (0 == errorCode2) {
    Serial.println("can2 ok");
  } else {
    Serial.print("Error can2: 0x");
    Serial.println(errorCode2, HEX);
  }

  frontWheel.m_initializeWheel(true, false);
  rearLeftWheel.m_initializeWheel(false, false);
  rearRightWheel.m_initializeWheel(false, false);
  Serial.println("Starting AMR");

  set_microros_native_ethernet_transports(local_mac, local_ip, agent_ip,
                                          agent_port);

  Serial.println("ROS Enable in Process!");

  allocator = rcl_get_default_allocator();
  RCCHECK(
      rclc_support_init(&support, 0, NULL, &allocator)); // create init_options
  RCCHECK(rclc_node_init_default(&node, NODE_NAME, "",
                                 &support)); // create node
                                             // create executor
  setup_all_publishers();
  setup_all_subscribers();
  setup_all_timers();
  setup_executor();
  digitalWrite(LED_BUILTIN, HIGH);
  Serial.println("AMR Initialization Complete!");
}

void loop() {
  if (wheel_pose_control_msg_recieved == true) {
    for (uint8_t idx = 0; idx < 3; idx++) {
      float travel_motor_speed_rpm =
          AMR_LINEAR_VELOCITY_MpS_TO_ANGULAR_VELOCITY_RPM(
              wheel_pose_control_msg.data.data[2 * idx]);
      float steer_angle_degrees =
          wheel_pose_control_msg.data.data[(2 * idx) + 1];
      wheels[idx]->m_setTravelSpeed_RPM(travel_motor_speed_rpm);
      wheels[idx]->m_setAbsoluteSteerToAngle_Degrees(steer_angle_degrees);
    }
    wheel_pose_control_msg_recieved = false;
  }

  if (ACAN_T4::can1.available()) {
    // Receive the message
    CANMessage message;
    ACAN_T4::can1.receive(message);
    const uint16_t message_idx =
        (uint16_t)interpret_can_bytes_to_decimal_big_indian(
            1.0f, 0.0f, 2, message.data[1], message.data[2]);

    switch (message.id) {
    case SDO_RECIEVE_MESSAGE_CAN_IDENTIFIER + FRONT_WHEEL_TRAVEL_MOTOR_NODE_ID:
    case SDO_RECIEVE_MESSAGE_CAN_IDENTIFIER + FRONT_WHEEL_STEER_MOTOR_NODE_ID:
      if (message_idx == ERROR_STATE_IDX) {
        if (frontWheel.m_parseErrorReadings(
                message.id, message.data[4], message.data[5],
                &motor_error_msg.data.data[0], &motor_error_msg.data.data[1]))
          publish_error_msg();
      } else if (message_idx == POS_ACTUAL_IDX) {
        frontWheel.m_parseEncoderReading(
            message.id, message.data[4], message.data[5], message.data[6],
            message.data[7], &raw_encoder_msg.data.data[0],
            &raw_encoder_msg.data.data[1]);
      }
      break;

    case SDO_RECIEVE_MESSAGE_CAN_IDENTIFIER +
        REAR_LEFT_WHEEL_TRAVEL_MOTOR_NODE_ID:
    case SDO_RECIEVE_MESSAGE_CAN_IDENTIFIER +
        REAR_LEFT_WHEEL_STEER_MOTOR_NODE_ID:
      if (message_idx == ERROR_STATE_IDX) {
        if (rearLeftWheel.m_parseErrorReadings(
                message.id, message.data[4], message.data[5],
                &motor_error_msg.data.data[2], &motor_error_msg.data.data[3]))
          publish_error_msg();
      } else if (message_idx == POS_ACTUAL_IDX) {
        rearLeftWheel.m_parseEncoderReading(
            message.id, message.data[4], message.data[5], message.data[6],
            message.data[7], &raw_encoder_msg.data.data[2],
            &raw_encoder_msg.data.data[3]);
      }
      break;

    case SDO_RECIEVE_MESSAGE_CAN_IDENTIFIER +
        REAR_RIGHT_WHEEL_TRAVEL_MOTOR_NODE_ID:
    case SDO_RECIEVE_MESSAGE_CAN_IDENTIFIER +
        REAR_RIGHT_WHEEL_STEER_MOTOR_NODE_ID:
      if (message_idx == ERROR_STATE_IDX) {
        if (rearRightWheel.m_parseErrorReadings(
                message.id, message.data[4], message.data[5],
                &motor_error_msg.data.data[4], &motor_error_msg.data.data[5]))
          publish_error_msg();
      } else if (message_idx == POS_ACTUAL_IDX) {
        rearRightWheel.m_parseEncoderReading(
            message.id, message.data[4], message.data[5], message.data[6],
            message.data[7], &raw_encoder_msg.data.data[4],
            &raw_encoder_msg.data.data[5]);
      }
      break;

    default:
      Serial.print("ID: ");
      Serial.println(message.id, HEX);
      Serial.print("HEX Message: ");
      for (int i = 0; i < 8; i++) {
        Serial.print(message.data[i], HEX);
        Serial.print(" ");
      }
      Serial.println();
      break;
    }
  }

  if (ACAN_T4::can3.available()) {
    // Receive the message
    CANMessage message;
    ACAN_T4::can3.receive(message);
    switch (message.id) {
    case BATT_ST_1:
      read_battery_status_1(message.data, &amr_battery_msg.data.data[0],
                            &amr_battery_msg.data.data[1],
                            &amr_battery_msg.data.data[2]);
      break;

    case CELL_VOLT:
      read_cell_voltage(message.data);
      break;

    case CELL_TEMP:
      read_cell_temperature(message.data);
      break;

    case BATT_ST_2:
      read_battery_status_2(message.data);
      break;

    case BMS_INFO:
      read_bms_information(message.data);
      break;

    case BMS_SW_STATUS:
      read_switch_status(message.data[0]);
      break;

    case CTRL_INFO:
      read_control_information(message.data);
      break;

    case BMSCHG_INFO:
      read_charging_request(message.data);
      break;

    default:
      Serial.print("ID: ");
      Serial.println(message.id, HEX);
      Serial.print("HEX Message: ");
      for (int i = 0; i < 8; i++) {
        Serial.print(message.data[i], HEX);
        Serial.print(" ");
      }
      Serial.println();
      break;
    }
  }
  RCSOFTCHECK(rclc_executor_spin_some(&executor, RCL_MS_TO_NS(1)));
}
