#include "wheel_control.hpp"
#include "ACAN_T4_CANMessage.h"
#include "core_pins.h"
#include "hex_to_decimal_convert.hpp"
#include "object_dictionary.hpp"
#include <cstdint>

#define TESTING_MODE_SPEED 500.0f
//-----------------------------------------------------------------//
// Class Constructor && Destructor
//-----------------------------------------------------------------//
/**
 * @brief Constructor for wheel class.
 * Controls 1 travel motor and 1 steer motor.
 *
 * @param travelMotorID NODE ID for travel motor. Less than 0x127
 * @param steerMotorID NODE ID for steer motor. Less than 0x127
 * @param invertTravelMotorDirection Invert Travel Motor Direction
 * @param invertSteeringMotorDirection Invert Steer Motor Direction
 * @param steeringEncoderOffset_Decimal Offset to zero the steer motor encoder
 * to counteract the encoder increments.
 * @param heatbeatTime_ms time to setup canopen heartbeat
 */
wheelControl::wheelControl(const uint8_t wheelID, const uint8_t travelMotorID,
                           const uint8_t steerMotorID,
                           const int32_t steeringEncoderOffset_Decimal,
                           const uint16_t heatbeatTime_ms)
    : m_wheelID(wheelID), m_heartBeatTime_ms(heatbeatTime_ms),
      m_travelMotorNodeID(travelMotorID), m_steerMotorNodeID(steerMotorID),
      m_steeringEncoderOffset_Decimal(steeringEncoderOffset_Decimal) {}

wheelControl::~wheelControl() = default;

//-----------------------------------------------------------------//
// Public Methods
//-----------------------------------------------------------------//

void wheelControl::m_initializeWheel(const bool invertTravelMotorDirection,
                                     const bool invertSteeringMotorDirection) {
  if (invertSteeringMotorDirection)
    m_invertSteering = -1;
  this->m_setupTravelMotor(invertTravelMotorDirection); // Setup Travel Motor
  this->m_setupSteeringMotor();                         // Setup Steer Motor
}

void wheelControl::m_enableTravelMotor(bool enable) {
  uint8_t controlword = CONTROLWORD_SPEED_MODE_ENABLE;
  if (!enable) {
    controlword = CONTROLWORD_DRIVER_OFF;
  }

  CANMessage message = this->m_generateMessage(
      m_operationType::DOWNLOAD, m_travelMotorNodeID, m_dataType::U16,
      CONTROLWORD_IDX, CONTROLWORD_SUB_IDX, controlword);
  ACAN_T4::can1.tryToSend(message);
  delay(CAN_MSG_DELAY);
}

void wheelControl::m_enableMotorNode(bool enable, uint8_t motorID) {
  CANMessage message;
  message.id = 0x00;
  message.len = 0x02;
  if (enable) {
    message.data[0] = 0x01;
  } else {
    message.data[0] = 0x02;
  }
  message.data[1] = m_travelMotorNodeID;
  ACAN_T4::can1.tryToSend(message);
  delay(CAN_MSG_DELAY);
}

void wheelControl::m_enableSteerMotor(bool enable) {
  uint8_t controlword = CONTROLWORD_POSITION_MODE_ABSOLUTE_ENABLE;
  if (!enable) {
    controlword = CONTROLWORD_POSITION_MODE_ABSOLUTE_OFF;
  }

  CANMessage message = this->m_generateMessage(
      m_operationType::DOWNLOAD, m_steerMotorNodeID, m_dataType::U16,
      CONTROLWORD_IDX, CONTROLWORD_SUB_IDX, controlword);
  ACAN_T4::can1.tryToSend(message);
  delay(CAN_MSG_DELAY);
}
void wheelControl::m_setProfileSpeedForSteerMotor_RPM(
    const float profileSpeed_RPM) {
  this->m_enableSteerMotor(false);
  float profileSpeed_RPM_decimal =
      profileSpeed_RPM * m_steering_pos_vel_conversion_factor;
  CANMessage message = this->m_generateMessage(
      m_operationType::DOWNLOAD, m_steerMotorNodeID, m_dataType::U32,
      PROFILE_SPEED_IDX, PROFILE_SPEED_SUB_IDX, profileSpeed_RPM_decimal);
  ACAN_T4::can1.tryToSend(message);
  delay(CAN_MSG_DELAY);
}

void wheelControl::m_setProfileAccelerationForTravelMotor_RPS(
    const float profileAccel_RPS, const float profileDeaccel_RPS) {
  {
    float profileAccel_RPS_decimal =
        profileAccel_RPS * m_travel_acc_dec_conversion_factor;
    CANMessage message = this->m_generateMessage(
        m_operationType::DOWNLOAD, m_travelMotorNodeID, m_dataType::U32,
        PROFILE_ACC_IDX, PROFILE_ACC_SUB_IDX, profileAccel_RPS_decimal);
    ACAN_T4::can1.tryToSend(message);
    delay(CAN_MSG_DELAY);
  }
  {
    float profileDeaccel_RPS_decimal =
        profileDeaccel_RPS * m_travel_acc_dec_conversion_factor;
    CANMessage message = this->m_generateMessage(
        m_operationType::DOWNLOAD, m_travelMotorNodeID, m_dataType::U32,
        PROFILE_DEC_IDX, PROFILE_DEC_SUB_IDX, profileDeaccel_RPS_decimal);
    ACAN_T4::can1.tryToSend(message);
    delay(CAN_MSG_DELAY);
  }
}

void wheelControl::m_setProfileAccelerationForSteerMotor_RPS(
    const float profileAccel_RPS, const float profileDeaccel_RPS) {
  {
    float profileAccel_RPS_decimal =
        profileAccel_RPS * m_steering_acc_dec_conversion_factor;
    CANMessage message = this->m_generateMessage(
        m_operationType::DOWNLOAD, m_steerMotorNodeID, m_dataType::U32,
        PROFILE_ACC_IDX, PROFILE_ACC_SUB_IDX, profileAccel_RPS_decimal);
    ACAN_T4::can1.tryToSend(message);
    delay(CAN_MSG_DELAY);
  }
  {
    float profileDeaccel_RPS_decimal =
        profileDeaccel_RPS * m_steering_acc_dec_conversion_factor;
    CANMessage message = this->m_generateMessage(
        m_operationType::DOWNLOAD, m_steerMotorNodeID, m_dataType::U32,
        PROFILE_DEC_IDX, PROFILE_DEC_SUB_IDX, profileDeaccel_RPS_decimal);
    ACAN_T4::can1.tryToSend(message);
    delay(CAN_MSG_DELAY);
  }
}

void wheelControl::m_setAbsoluteSteerToAngle_Degrees(
    const float absoluteSteeringAngle_Degrees) {

  if (absoluteSteeringAngle_Degrees > MAX_STEER_ANGLE ||
      absoluteSteeringAngle_Degrees < -MAX_STEER_ANGLE) {
    Serial.println("Cannot set the steer Angle. Out of bounds!!");
    return;
  }

  if (m_currentSteeringAngle_deg == absoluteSteeringAngle_Degrees)
    return;

  this->m_enableSteerMotor(false);

  const int32_t steerPosition_deg_decimal =
      (int32_t)(m_invertSteering * absoluteSteeringAngle_Degrees *
                m_steering_pos_vel_conversion_factor) +
      m_steeringEncoderOffset_Decimal;

  CANMessage message = this->m_generateMessage(
      m_operationType::DOWNLOAD, m_steerMotorNodeID, m_dataType::I32,
      TARGET_POSITION_IDX, TARGET_POSITION_SUB_IDX, steerPosition_deg_decimal);

  ACAN_T4::can1.tryToSend(message);
  delay(CAN_MSG_DELAY);

  this->m_enableSteerMotor(true);
  m_currentSteeringAngle_deg = absoluteSteeringAngle_Degrees;
}

void wheelControl::m_setTravelSpeed_RPM(const float travelSpeed) {
#ifdef TESTING_MODE
  if (travelSpeed > TESTING_MODE_SPEED || travelSpeed < -TESTING_MODE_SPEED) {
    Serial.println("Cannot set the Travel Speed. Out of bounds!!");
    return;
  }
#endif
  if (travelSpeed > MAX_TRAVEL_SPEED_RPM ||
      travelSpeed < -MAX_TRAVEL_SPEED_RPM) {
    Serial.println("Cannot set the Travel Speed. Out of bounds!!");
    return;
  }
  if (m_currentTravelSpeed_RPM == travelSpeed) {
    return;
  } else {
    m_currentTravelSpeed_RPM = travelSpeed;
  }

  const int32_t speed_rpm_decimal =
      (int32_t)(travelSpeed * m_travel_pos_vel_conversion_factor);

  uint8_t newDirection = (this->m_travelMotor_Direction == INVERT_DIR_CW)
                             ? INVERT_DIR_CW
                             : INVERT_DIR_CCW;
  {
    CANMessage message = this->m_generateMessage(
        m_operationType::DOWNLOAD, m_travelMotorNodeID, m_dataType::U8,
        INVERT_DIR_IDX, INVERT_DIR_SUB_IDX, newDirection);
    ACAN_T4::can1.tryToSend(message);
    delay(CAN_MSG_DELAY);
  }
  {
    CANMessage message = this->m_generateMessage(
        m_operationType::DOWNLOAD, m_travelMotorNodeID, m_dataType::I32,
        TARGET_SPEED_IDX, TARGET_SPEED_SUB_IDX, speed_rpm_decimal);
    ACAN_T4::can1.tryToSend(message);
    delay(CAN_MSG_DELAY);
  }

  this->m_enableTravelMotor(true);
}

void wheelControl::m_setNodeHeartbeatTime(const uint8_t nodeID,
                                          const uint16_t heartBeatTime_ms) {
  CANMessage message = this->m_generateMessage(
      m_operationType::DOWNLOAD, m_travelMotorNodeID, m_dataType::U16,
      PRODUCER_HEARTBEAT_TIME_IDX, PRODUCER_HEARTBEAT_TIME_SUB_IDX,
      heartBeatTime_ms);
  ACAN_T4::can1.tryToSend(message);
  delay(CAN_MSG_DELAY);
}

void wheelControl::m_emergencyStopWheel() {
  {
    CANMessage message = this->m_generateMessage(
        m_operationType::DOWNLOAD, m_travelMotorNodeID, m_dataType::U16,
        CONTROLWORD_IDX, CONTROLWORD_SUB_IDX, CONTROLWORD_DRIVER_OFF);
    ACAN_T4::can1.tryToSend(message);
    delay(CAN_MSG_DELAY);
  }
  {
    CANMessage message = this->m_generateMessage(
        m_operationType::DOWNLOAD, m_steerMotorNodeID, m_dataType::U16,
        CONTROLWORD_IDX, CONTROLWORD_SUB_IDX, CONTROLWORD_DRIVER_OFF);
    ACAN_T4::can1.tryToSend(message);
    delay(CAN_MSG_DELAY);
  }
}

void wheelControl::m_requestErrorReadings() {
  {
    CANMessage message = this->m_generateMessage(
        m_operationType::SDO_READ, m_travelMotorNodeID, m_dataType::U16,
        ERROR_STATE_IDX, ERROR_STATE_SUB_IDX, 0);
    ACAN_T4::can1.tryToSend(message);
  }
  {
    CANMessage message = this->m_generateMessage(
        m_operationType::SDO_READ, m_steerMotorNodeID, m_dataType::U16,
        ERROR_STATE_IDX, ERROR_STATE_SUB_IDX, 0);
    ACAN_T4::can1.tryToSend(message);
  }
}

// TODO: Make a single function to process the data
bool wheelControl::m_parseErrorReadings(const uint16_t nodeID,
                                        const uint8_t LSB, const uint8_t MSB,
                                        uint16_t *const travelMotorError,
                                        uint16_t *const steerMotorError) {
  /**
   * bits[0] -> Extended Error
   * bits[1] -> Encoder ABZ/not connected
   * bits[2] -> Encoder UVW/Encoder internal
   * bits[3] -> Encoder Counting/ Encoder CRC
   * bits[4] -> Driver Temperature
   * bits[5] -> Over Voltage
   * bits[6] -> Under Voltage
   * bits[7] -> Over Current
   * bits[8] -> Chop Resistor
   * bits[9] -> Position Following
   * bits[10] -> Low Logic Voltage
   * bits[11] -> Motors or Driver IIt
   * bits[12] -> Over Frequency
   * bits[13] -> Motor Temperature
   * bits[14] -> Motor Communication
   * bits[15] -> EEPROM Data
   */

  // Combine LSB and MSB into a single 16-bit value
  const uint16_t error_status = (static_cast<uint16_t>(MSB) << 8) | LSB;

  if (nodeID == m_travelMotorNodeID + SDO_RECIEVE_MESSAGE_CAN_IDENTIFIER) {
    *travelMotorError = error_status;
  } else if (nodeID ==
             m_steerMotorNodeID + SDO_RECIEVE_MESSAGE_CAN_IDENTIFIER) {
    *steerMotorError = error_status;
  }

  if (error_status > 0) {
    Serial.print("Motor ID: ");
    Serial.println(nodeID, HEX);
    Serial.print("Error: ");
    Serial.println(error_status, BIN);
    return true;
  } else
    return false;
}

void wheelControl::m_requestEncoderReadings() {
  {
    CANMessage message = m_generateMessage(
        m_operationType::SDO_READ, m_travelMotorNodeID, m_dataType::I32,
        POS_ACTUAL_IDX, POS_ACTUAL_SUB_IDX, 0);
    ACAN_T4::can1.tryToSend(message);
  }
  {
    CANMessage message = m_generateMessage(
        m_operationType::SDO_READ, m_steerMotorNodeID, m_dataType::I32,
        POS_ACTUAL_IDX, POS_ACTUAL_SUB_IDX, 0);
    ACAN_T4::can1.tryToSend(message);
  }
}

void wheelControl::m_parseEncoderReading(
    const uint16_t nodeID, const uint8_t message1, const uint8_t message2,
    const uint8_t message3, const uint8_t message4,
    int32_t *const travelMotorReading, int32_t *const steerMotorReading) {

  const int32_t encoder_reading = interpret_can_bytes_to_decimal_big_indian(
      1.0f, 0.0f, 4, message1, message2, message3, message4);
  if (nodeID == m_travelMotorNodeID + SDO_RECIEVE_MESSAGE_CAN_IDENTIFIER) {
    *travelMotorReading = encoder_reading;
  } else if (nodeID ==
             m_steerMotorNodeID + SDO_RECIEVE_MESSAGE_CAN_IDENTIFIER) {
    *steerMotorReading = encoder_reading;
  }
}
//-----------------------------------------------------------------//
// Private Methods
//-----------------------------------------------------------------//

const CANMessage
wheelControl::m_generateMessage(const m_operationType operationType,
                                const uint8_t nodeID, const m_dataType dataType,
                                const uint16_t index, const uint8_t subIndex,
                                const uint32_t data) {
  CANMessage message;
  message.id = SDO_SEND_MESSAGE_CAN_IDENTIFIER + nodeID;
  message.len = 0x08;
  switch (operationType) {
  case m_operationType::DOWNLOAD:
    switch (dataType) {
    case m_dataType::U8:
      message.data[0] = DOWNLOAD_COMMAND_WORD_8_BITS;
      break;
    case m_dataType::I8:
      message.data[0] = DOWNLOAD_COMMAND_WORD_8_BITS;
      break;
    case m_dataType::U16:
      message.data[0] = DOWNLOAD_COMMAND_WORD_16_BITS;
      break;
    case m_dataType::I16:
      message.data[0] = DOWNLOAD_COMMAND_WORD_16_BITS;
      break;
    case m_dataType::U32:
      message.data[0] = DOWNLOAD_COMMAND_WORD_32_BITS;
      break;
    case m_dataType::I32:
      message.data[0] = DOWNLOAD_COMMAND_WORD_32_BITS;
      break;
    default:
      Serial.println("Cannot Handle DataType");
      break;
    }
    break;

  case m_operationType::SDO_READ:
    message.data[0] = SDO_READ_SEND_PARAMETER_CMD_WORD;
    break;

  default:
    Serial.println("Unknown Operation Type!");
    break;
  }
  message.data[1] = index & 0xFF;
  message.data[2] = (index >> 8) & 0xFF;
  message.data[3] = subIndex;
  message.data[4] = data & 0xFF;
  message.data[5] = (data >> 8) & 0xFF;
  message.data[6] = (data >> 16) & 0xFF;
  message.data[7] = (data >> 24) & 0xFF;

  return message;
}

void wheelControl::m_setupTravelMotor(bool invertDirection = false) {
  Serial.print("Travel Motor ID: 0x");
  Serial.println(this->m_travelMotorNodeID, HEX);

  // Set Producer_Heartbeat_Time
  this->m_setNodeHeartbeatTime(this->m_travelMotorNodeID,
                               this->m_heartBeatTime_ms);

  { // Set TX1_ID to 0x180+Node ID
    CANMessage message = this->m_generateMessage(
        m_operationType::DOWNLOAD, m_travelMotorNodeID, m_dataType::U32,
        TPDO1_ID_IDX, TPDO1_ID_SUB_IDX,
        TPDO1_RECIEVE_MESSAGE_CAN_IDENTIFIER + m_travelMotorNodeID);

    ACAN_T4::can1.tryToSend(message);
    delay(CAN_MSG_DELAY);
  }

  // Invert Motor Direction
  if (invertDirection) {
    CANMessage message = this->m_generateMessage(
        m_operationType::DOWNLOAD, m_travelMotorNodeID, m_dataType::U8,
        INVERT_DIR_IDX, INVERT_DIR_SUB_IDX, INVERT_DIR_CW);
    ACAN_T4::can1.tryToSend(message);
    this->m_travelMotor_Direction = INVERT_DIR_CW;
    delay(CAN_MSG_DELAY);
  } else {
    CANMessage message = this->m_generateMessage(
        m_operationType::DOWNLOAD, m_travelMotorNodeID, m_dataType::U8,
        INVERT_DIR_IDX, INVERT_DIR_SUB_IDX, INVERT_DIR_CCW);
    ACAN_T4::can1.tryToSend(message);
    this->m_travelMotor_Direction = INVERT_DIR_CCW;
    delay(CAN_MSG_DELAY);
  }

  this->m_enableTravelMotor(false);

  this->m_enableMotorNode(true, m_travelMotorNodeID); // Enable Motor Node

  Serial.print("Travel Motor Setup Complete: ");
  Serial.println(m_wheelID);
}

void wheelControl::m_setupSteeringMotor() {
  Serial.print("Steering Motor ID: 0x");
  Serial.println(this->m_steerMotorNodeID, HEX);
  // Set Producer_Heartbeat_Time
  this->m_setNodeHeartbeatTime(m_steerMotorNodeID, this->m_heartBeatTime_ms);
  CANMessage message = this->m_generateMessage(
      m_operationType::DOWNLOAD, m_steerMotorNodeID, m_dataType::U32,
      TPDO1_ID_IDX, TPDO1_ID_SUB_IDX,
      TPDO1_RECIEVE_MESSAGE_CAN_IDENTIFIER + m_steerMotorNodeID);
  ACAN_T4::can1.tryToSend(message);
  delay(CAN_MSG_DELAY);
  this->m_enableSteerMotor(false);
  // set Motor Profile Speed to 100RPM
  this->m_setProfileSpeedForSteerMotor_RPM(STEER_PROFILE_SPEED);
  this->m_setAbsoluteSteerToAngle_Degrees(0.0f);
  this->m_enableMotorNode(true, m_travelMotorNodeID); // Enable Motor Node
  Serial.print("Steer Motor Setup Complete: ");
  Serial.println(m_wheelID);
}
