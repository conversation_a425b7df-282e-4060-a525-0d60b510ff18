#include "hex_to_decimal_convert.hpp"
#include <stdarg.h>
#include <stdint.h>

float interpret_can_bytes_to_decimal_big_indian(const float resolution,
                                                const float offset,
                                                const int count, ...) {
  va_list args;
  va_start(args, count);
  int64_t hexValue = 0;
  for (uint8_t idx = 0; idx < count; idx++) {
    hexValue |= va_arg(args, int) << (8 * idx);
  }
  float converted_value = (float)hexValue * resolution;
  converted_value += offset;
  return converted_value;
}
float interpret_can_bytes_to_decimal_little_indian(const float resolution,
                                                   const float offset,
                                                   const int count, ...) {
  va_list args;
  va_start(args, count);
  int64_t hexValue = 0;
  for (uint8_t idx = 0; idx < count; idx++) {
    hexValue |= va_arg(args, int) << (8 * (count - idx - 1));
  }
  float converted_value = (float)hexValue * resolution;
  converted_value += offset;
  return converted_value;
}
