#ifndef CONGIG_HPP
#define CONGIG_HPP

#define MOTOR_HEARTBEAT_TIME_MS 1000

#define AMR_WHEEL_DIAMETER_MM 155.0f
#define AMR_WHEEL_RADIUS_MM (AMR_WHEEL_DIAMETER_MM / 2.0f)
#define AMR_LINEAR_VELOCITY_MpS_TO_ANGULAR_VELOCITY_RPM(v)                     \
  (((v)*60.0) / (2.0 * M_PI * AMR_WHEEL_RADIUS_MM / 1000.0f))

// Travelling Motor Configuration
#define TRAVEL_ENCODER_RESOLUTION 10000.0f
#define TRAVEL_MOTOR_GEAR_RATIO 45.5f
#define MAX_TRAVEL_SPEED_RPM 2500.0f

// Steering Motor Configuration
#define STEERING_ENCODER_RESOLUTION 92500.0f
#define MAX_STEER_ANGLE 140.0f
#define STEER_PROFILE_SPEED 250.0f

#endif // CONGIG_HPP
