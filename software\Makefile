# CREATED BY VIM-PIO
all:
	platformio -f -c vim run

upload:
	platformio -f -c vim run --target upload

clean:
	platformio -f -c vim run --target clean

ros_clean:
	platformio -f -c vim run --target clean_microros

program:
	platformio -f -c vim run --target program

uploadfs:
	platformio -f -c vim run --target uploadfs

check:
	platformio -f -c vim check --flags "-DDEBUG cppcheck: --std=c++11" --skip-packages --fail-on-defect=low
