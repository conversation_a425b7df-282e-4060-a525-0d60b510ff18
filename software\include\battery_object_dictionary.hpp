#ifndef BATTERY_OBJECT_DICTIONARY
#define BATTERY_OBJECT_DICTIONARY

#define BATT_ST_1 0x02F4 // Battery Status Information 1 (Standard frame, 20ms)
#define CELL_VOLT 0x04F4 // Cell Voltage (Standard frame, 100ms)
#define CELL_TEMP 0x05F4 // Battery Temperature (Standard frame, 500ms)
#define ALM_INFO 0x07F4  // Warning Information (Standard frame, 100ms)
#define BATT_ST_2                                                              \
  0x18F128F4 // Battery Status Information 2 (Extended frame, 100ms)
#define ALL_TEMP                                                               \
  0x18F228F4 // All Temperatures of the Cell (Extended frame, 500ms)
#define BMSERR_INFO 0x18F328F4   // BMS Fault Message (Extended frame, 100ms)
#define BMS_INFO 0x18F428F4      // BMS Information (Extended frame, 500ms)
#define BMS_SW_STATUS 0x18F528F4 // BMS Switch Status (Extended frame, 500ms)
#define CELL_VOL 0x18E028F4      // Cell Voltage (Extended frame, 1000ms)
#define BMSCHG_INFO 0x1806E5F4   // BMS Charging Request (Extended frame, 500ms)
#define CTRL_INFO                                                              \
  0x18F0F428 // Control Information (Extended frame, BMS peripheral)

#endif // BATTERY_OBJECT_DICTIONARY
